const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// 模拟Univer API
const mockUniverAPI = {
  getActiveWorkbook: () => ({
    getActiveSheet: () => ({
      getRange: (cell) => ({
        getValue: () => null, // 没有值
        getCellData: () => null, // 没有数据
        getNumberFormat: () => '', // 没有格式
        getCellStyleData: () => ({}), // 没有样式
        getValues: () => [[]], // 空数据
      }),
      hasFilter: () => false, // 没有筛选器
      getSheetCount: () => 1 // 只有一个工作表
    })
  })
};

// 创建验证服务类
class ExcelValidationService {
  constructor(univerAPI) {
    this.univerAPI = univerAPI;
  }

  async validateTask(rule) {
    try {
      switch (rule.type) {
        case 'cellValue':
        case 'input':
          return await this.validateCellValue(rule);
        case 'cellFormula':
          return await this.validateCellFormula(rule);
        case 'cellFormat':
          return await this.validateCellFormat(rule);
        case 'cellStyle':
          return await this.validateCellStyle(rule);
        case 'chart':
          return await this.validateChart(rule);
        case 'pivotTable':
          return await this.validatePivotTable(rule);
        case 'sort':
          return await this.validateSort(rule);
        case 'multiSort':
          return await this.validateMultiSort(rule);
        default:
          return {
            success: false,
            message: `未知的验证类型: ${rule.type}`
          };
      }
    } catch (error) {
      return {
        success: false,
        message: '验证过程中发生错误，请重试'
      };
    }
  }

  async validateCellValue(rule) {
    if (!rule.cell || rule.expectedValue === undefined) {
      return { success: false, message: '验证规则配置错误：缺少单元格位置或期望值' };
    }

    const workbook = this.univerAPI.getActiveWorkbook();
    const worksheet = workbook.getActiveSheet();
    const range = worksheet.getRange(rule.cell);
    const cellValue = range.getValue();
    const actualValue = cellValue?.v || cellValue;

    const isMatch = actualValue === rule.expectedValue;
    return {
      success: isMatch,
      message: isMatch ? '单元格值验证通过！' : `单元格值不正确。期望: "${rule.expectedValue}"，实际: "${actualValue}"`
    };
  }

  async validateCellFormula(rule) {
    if (!rule.cell || !rule.expectedFormula) {
      return { success: false, message: '验证规则配置错误：缺少单元格位置或期望公式' };
    }

    const workbook = this.univerAPI.getActiveWorkbook();
    const worksheet = workbook.getActiveSheet();
    const range = worksheet.getRange(rule.cell);
    const cellData = range.getCellData();
    const actualFormula = cellData?.f || '';

    const isMatch = actualFormula.toUpperCase() === rule.expectedFormula.toUpperCase();
    return {
      success: isMatch,
      message: isMatch ? '公式验证通过！' : `公式不正确。期望: "${rule.expectedFormula}"，实际: "${actualFormula}"`
    };
  }

  async validateCellFormat(rule) {
    if (!rule.cell || !rule.expectedFormat) {
      return { success: false, message: '验证规则配置错误：缺少单元格位置或期望格式' };
    }

    const workbook = this.univerAPI.getActiveWorkbook();
    const worksheet = workbook.getActiveSheet();
    const range = worksheet.getRange(rule.cell);
    const numberFormat = range.getNumberFormat() || '';

    let isFormatMatch = false;
    switch (rule.expectedFormat) {
      case 'currency':
        isFormatMatch = numberFormat.includes('¥') || numberFormat.includes('$');
        break;
      default:
        isFormatMatch = numberFormat === rule.expectedFormat;
    }

    return {
      success: isFormatMatch,
      message: isFormatMatch ? '格式验证通过！' : `格式不正确。期望: ${rule.expectedFormat}，实际: "${numberFormat}"`
    };
  }

  async validateCellStyle(rule) {
    if (!rule.cell || !rule.expectedStyle) {
      return { success: false, message: '验证规则配置错误：缺少单元格位置或期望样式' };
    }

    const workbook = this.univerAPI.getActiveWorkbook();
    const worksheet = workbook.getActiveSheet();
    const range = worksheet.getRange(rule.cell);
    const style = range.getCellStyleData() || {};

    let allMatch = true;
    if (rule.expectedStyle.bold !== undefined) {
      const isBold = style.bl === 1 || style.bold === true;
      if (isBold !== rule.expectedStyle.bold) allMatch = false;
    }
    if (rule.expectedStyle.italic !== undefined) {
      const isItalic = style.it === 1 || style.italic === true;
      if (isItalic !== rule.expectedStyle.italic) allMatch = false;
    }

    return {
      success: allMatch,
      message: allMatch ? '样式验证通过！' : '样式不正确'
    };
  }

  async validateChart(rule) {
    // 简单检查DOM中是否有图表元素
    if (typeof document !== 'undefined') {
      const chartElements = document.querySelectorAll('.univer-chart, .echarts-chart, canvas[data-zr-dom-id]');
      if (chartElements.length > 0) {
        return { success: true, message: '图表创建成功！' };
      }
    }

    return {
      success: false,
      message: '未找到图表。请按照操作步骤创建图表。'
    };
  }

  async validatePivotTable(rule) {
    const workbook = this.univerAPI.getActiveWorkbook();
    const worksheetCount = workbook.getSheetCount ? workbook.getSheetCount() : 1;

    if (worksheetCount > 1) {
      return { success: true, message: '数据透视表创建成功！' };
    }

    // 检查DOM中是否有透视表元素
    if (typeof document !== 'undefined') {
      const pivotElements = document.querySelectorAll('.pivot-table, .univer-pivot-table');
      if (pivotElements.length > 0) {
        return { success: true, message: '数据透视表创建成功！' };
      }
    }

    return {
      success: false,
      message: '未检测到数据透视表。请按照操作步骤创建透视表。'
    };
  }

  async validateSort(rule) {
    if (!rule.dataRange && !rule.expectedOrder) {
      return { success: false, message: '验证规则配置错误：缺少数据范围或期望顺序' };
    }

    const workbook = this.univerAPI.getActiveWorkbook();
    const worksheet = workbook.getActiveSheet();
    const range = worksheet.getRange(rule.dataRange || 'A1:C6');
    const values = range.getValues();

    // 如果没有数据或数据为空，说明没有进行排序操作
    if (!values || values.length < 2) {
      return {
        success: false,
        message: '数据范围内没有足够的数据进行排序验证'
      };
    }

    return {
      success: false,
      message: '排序验证：数据未按指定顺序排序'
    };
  }

  async validateMultiSort(rule) {
    if (!rule.sorts || rule.sorts.length === 0) {
      return { success: false, message: '未指定排序条件' };
    }

    return {
      success: false,
      message: '多列排序验证：数据未按指定条件排序'
    };
  }
}

async function testRound1() {
  try {
    console.log('开始测试...');
    console.log('='.repeat(60));
    console.log('第一轮测试：不做任何操作，验证任务是否能完成（应该不能完成）');
    console.log('='.repeat(60));

    // 获取需要测试的任务（除了工作正常的任务）
    const tasks = await prisma.task.findMany({
      where: {
        NOT: {
          name: {
            in: ['单列筛选', '多列筛选', '数值条件格式', '多条件格式']
          }
        }
      },
      orderBy: { id: 'asc' }
    });

    console.log(`找到 ${tasks.length} 个需要测试的任务`);

    const validationService = new ExcelValidationService(mockUniverAPI);
    let passedCount = 0;
    let failedCount = 0;

    for (const task of tasks) {
      console.log(`\n测试任务: ${task.name} (${task.type})`);
      
      try {
        const validationRule = JSON.parse(task.validation);
        const result = await validationService.validateTask(validationRule);
        
        if (result.success) {
          console.log(`❌ 错误：任务 "${task.name}" 在没有操作的情况下通过了验证`);
          passedCount++;
        } else {
          console.log(`✅ 正确：任务 "${task.name}" 正确地检测到未完成`);
          console.log(`   消息: ${result.message.substring(0, 100)}...`);
          failedCount++;
        }
      } catch (error) {
        console.log(`⚠️  任务 "${task.name}" 验证过程中出错: ${error.message}`);
      }
    }

    console.log('\n' + '='.repeat(60));
    console.log('第一轮测试结果汇总:');
    console.log(`✅ 正确检测到未完成: ${failedCount} 个任务`);
    console.log(`❌ 错误地通过验证: ${passedCount} 个任务`);
    console.log(`总测试任务数: ${tasks.length}`);
    
    if (passedCount === 0) {
      console.log('🎉 第一轮测试全部通过！所有任务都正确地检测到未完成状态。');
    } else {
      console.log('⚠️  第一轮测试发现问题，有任务在没有操作时错误地通过了验证。');
    }

  } catch (error) {
    console.error('测试过程中发生错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testRound1();
