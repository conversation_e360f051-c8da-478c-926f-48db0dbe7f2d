const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function getAllTasks() {
  try {
    const tasks = await prisma.task.findMany({
      include: {
        level: true
      },
      orderBy: [
        { level: { order: 'asc' } },
        { order: 'asc' }
      ]
    });
    
    console.log('所有任务列表:');
    console.log('='.repeat(80));
    
    for (const task of tasks) {
      console.log('任务名称:', task.name);
      console.log('任务类型:', task.type);
      console.log('关卡:', task.level.name);
      console.log('验证规则:', task.validation);
      console.log('-'.repeat(40));
    }
  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

getAllTasks();
